#!/bin/bash

# GitLab DAP Tracing Validation Script
# Validates that all tracing components are working correctly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

TRACE_DIR="dap-traces"
GDK_PATH="../gdk"

print_header() {
    echo -e "${PURPLE}🧪 GitLab DAP Tracing Validation${NC}"
    echo -e "${PURPLE}=================================${NC}"
}

print_test() {
    echo -e "${BLUE}🔍 $1${NC}"
}

print_pass() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_fail() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Test 1: Check if tracing directory exists
test_trace_directory() {
    print_test "Testing trace directory structure..."
    
    if [ -d "$TRACE_DIR" ]; then
        print_pass "Trace directory exists"
    else
        print_fail "Trace directory not found. Run './dap-enhanced-tracing.sh start' first"
        return 1
    fi
    
    # Check subdirectories
    for subdir in filtered correlation agents tools pids; do
        if [ -d "$TRACE_DIR/$subdir" ]; then
            print_pass "Subdirectory $subdir exists"
        else
            print_fail "Subdirectory $subdir missing"
            return 1
        fi
    done
    
    return 0
}

# Test 2: Check if configuration files were created
test_configuration_files() {
    print_test "Testing configuration files..."
    
    local files=(
        "$TRACE_DIR/ai-gateway-enhanced.env"
        "$TRACE_DIR/duo-workflow-enhanced.env"
        "vscode-dap-debug-config.json"
        "vscode-dap-settings.json"
    )
    
    for file in "${files[@]}"; do
        if [ -f "$file" ]; then
            print_pass "Configuration file $file exists"
        else
            print_fail "Configuration file $file missing"
            return 1
        fi
    done
    
    return 0
}

# Test 3: Check if services are running
test_services_running() {
    print_test "Testing service availability..."
    
    # Test GitLab
    if curl -s -I http://localhost:3000 > /dev/null 2>&1; then
        print_pass "GitLab is running on port 3000"
    else
        print_fail "GitLab is not running on port 3000"
        return 1
    fi
    
    # Test AI Gateway
    if curl -s -I http://localhost:5052/monitoring/healthz > /dev/null 2>&1; then
        print_pass "AI Gateway is running on port 5052"
    else
        print_warning "AI Gateway might not be running on port 5052"
    fi
    
    # Test Duo Workflow Service (check if port is listening)
    if lsof -i :50051 > /dev/null 2>&1; then
        print_pass "Duo Workflow Service is listening on port 50051"
    else
        print_warning "Duo Workflow Service might not be running on port 50051"
    fi
    
    return 0
}

# Test 4: Check if log files exist and are being written
test_log_files() {
    print_test "Testing log file availability..."
    
    local log_files=(
        "$GDK_PATH/gitlab/log/development.log"
        "$GDK_PATH/log/gitlab-ai-gateway/gateway_debug.log"
        "$GDK_PATH/log/duo-workflow-service/current"
    )
    
    for log_file in "${log_files[@]}"; do
        if [ -f "$log_file" ]; then
            # Check if file was modified recently (within last 10 minutes)
            if [ $(find "$log_file" -mmin -10 | wc -l) -gt 0 ]; then
                print_pass "Log file $log_file exists and is recent"
            else
                print_warning "Log file $log_file exists but hasn't been updated recently"
            fi
        else
            print_warning "Log file $log_file not found"
        fi
    done
    
    return 0
}

# Test 5: Check if filtering processes are running
test_filtering_processes() {
    print_test "Testing log filtering processes..."
    
    local pid_files=(
        "$TRACE_DIR/pids/ai-gateway-filter.pid"
        "$TRACE_DIR/pids/duo-workflow-filter.pid"
        "$TRACE_DIR/pids/gitlab-filter.pid"
        "$TRACE_DIR/pids/correlation-tracker.pid"
        "$TRACE_DIR/pids/agent-monitor.pid"
    )
    
    local running_count=0
    for pid_file in "${pid_files[@]}"; do
        if [ -f "$pid_file" ]; then
            local pid=$(cat "$pid_file")
            if kill -0 "$pid" 2>/dev/null; then
                print_pass "Process $(basename "$pid_file" .pid) is running (PID: $pid)"
                ((running_count++))
            else
                print_fail "Process $(basename "$pid_file" .pid) is not running"
            fi
        else
            print_fail "PID file $pid_file not found"
        fi
    done
    
    if [ $running_count -gt 0 ]; then
        print_pass "$running_count filtering processes are running"
        return 0
    else
        print_fail "No filtering processes are running"
        return 1
    fi
}

# Test 6: Check if filtered logs are being generated
test_filtered_logs() {
    print_test "Testing filtered log generation..."
    
    local filtered_logs=(
        "$TRACE_DIR/filtered/ai-gateway-dap.log"
        "$TRACE_DIR/filtered/duo-workflow-agents.log"
        "$TRACE_DIR/filtered/gitlab-dap.log"
    )
    
    local logs_with_content=0
    for log_file in "${filtered_logs[@]}"; do
        if [ -f "$log_file" ]; then
            local line_count=$(wc -l < "$log_file" 2>/dev/null || echo 0)
            if [ "$line_count" -gt 0 ]; then
                print_pass "Filtered log $(basename "$log_file") has $line_count lines"
                ((logs_with_content++))
            else
                print_warning "Filtered log $(basename "$log_file") exists but is empty"
            fi
        else
            print_warning "Filtered log $(basename "$log_file") not found"
        fi
    done
    
    if [ $logs_with_content -gt 0 ]; then
        print_pass "$logs_with_content filtered logs contain data"
        return 0
    else
        print_warning "No filtered logs contain data yet (this is normal if no DAP requests have been made)"
        return 0
    fi
}

# Test 7: Check if analysis tools are executable
test_analysis_tools() {
    print_test "Testing analysis tools..."
    
    local tools=(
        "$TRACE_DIR/dashboard.sh"
        "$TRACE_DIR/trace-correlation.sh"
        "$TRACE_DIR/analyze-agent-flow.sh"
        "$TRACE_DIR/correlation-tracker.sh"
        "$TRACE_DIR/agent-monitor.sh"
        "dap-trace-analyzer.py"
    )
    
    for tool in "${tools[@]}"; do
        if [ -f "$tool" ]; then
            if [ -x "$tool" ]; then
                print_pass "Tool $(basename "$tool") is executable"
            else
                print_fail "Tool $(basename "$tool") is not executable"
                return 1
            fi
        else
            print_fail "Tool $(basename "$tool") not found"
            return 1
        fi
    done
    
    return 0
}

# Test 8: Test Python dependencies for dashboard
test_python_dependencies() {
    print_test "Testing Python dependencies..."
    
    # Test if Python 3 is available
    if command -v python3 >/dev/null 2>&1; then
        print_pass "Python 3 is available"
    else
        print_fail "Python 3 is not available"
        return 1
    fi
    
    # Test required Python modules
    local modules=("json" "re" "pathlib" "argparse")
    for module in "${modules[@]}"; do
        if python3 -c "import $module" 2>/dev/null; then
            print_pass "Python module '$module' is available"
        else
            print_fail "Python module '$module' is not available"
            return 1
        fi
    done
    
    # Test optional modules for web dashboard
    if python3 -c "import flask, flask_cors" 2>/dev/null; then
        print_pass "Flask dependencies available for web dashboard"
    else
        print_warning "Flask dependencies not available. Install with: pip install flask flask-cors"
    fi
    
    return 0
}

# Test 9: Validate VS Code configuration
test_vscode_configuration() {
    print_test "Testing VS Code configuration..."
    
    local vscode_dir="../gitlab-vscode-extension"
    
    if [ -d "$vscode_dir" ]; then
        print_pass "VS Code extension directory found"
        
        # Check if configuration files can be copied
        if [ -f "vscode-dap-debug-config.json" ] && [ -f "vscode-dap-settings.json" ]; then
            print_pass "VS Code configuration files are ready to be applied"
            print_info "To apply: cp vscode-dap-*.json $vscode_dir/.vscode/"
        else
            print_fail "VS Code configuration files not found"
            return 1
        fi
    else
        print_warning "VS Code extension directory not found at $vscode_dir"
    fi
    
    return 0
}

# Test 10: Test dashboard HTML
test_dashboard_html() {
    print_test "Testing dashboard HTML..."
    
    if [ -f "dap-debug-dashboard.html" ]; then
        # Basic HTML validation
        if grep -q "<html" "dap-debug-dashboard.html" && grep -q "</html>" "dap-debug-dashboard.html"; then
            print_pass "Dashboard HTML file is valid"
        else
            print_fail "Dashboard HTML file appears to be invalid"
            return 1
        fi
    else
        print_fail "Dashboard HTML file not found"
        return 1
    fi
    
    return 0
}

# Run all tests
run_all_tests() {
    print_header
    
    local tests=(
        "test_trace_directory"
        "test_configuration_files"
        "test_services_running"
        "test_log_files"
        "test_filtering_processes"
        "test_filtered_logs"
        "test_analysis_tools"
        "test_python_dependencies"
        "test_vscode_configuration"
        "test_dashboard_html"
    )
    
    local passed=0
    local failed=0
    local warnings=0
    
    for test in "${tests[@]}"; do
        echo ""
        if $test; then
            ((passed++))
        else
            ((failed++))
        fi
    done
    
    echo ""
    echo -e "${PURPLE}=================================${NC}"
    echo -e "${GREEN}✅ Tests Passed: $passed${NC}"
    echo -e "${RED}❌ Tests Failed: $failed${NC}"
    
    if [ $failed -eq 0 ]; then
        echo ""
        print_pass "All tests passed! DAP tracing is ready to use."
        echo ""
        print_info "Next steps:"
        print_info "1. Apply service configurations and restart services"
        print_info "2. Launch VS Code extension with enhanced debugging"
        print_info "3. Start dashboard: ./dap-traces/dashboard.sh"
        print_info "4. Test with DAP requests in VS Code"
        return 0
    else
        echo ""
        print_fail "Some tests failed. Please address the issues above."
        return 1
    fi
}

# Main execution
case "${1:-all}" in
    "all")
        run_all_tests
        ;;
    "services")
        test_services_running
        ;;
    "logs")
        test_log_files
        test_filtered_logs
        ;;
    "processes")
        test_filtering_processes
        ;;
    "tools")
        test_analysis_tools
        ;;
    "help"|*)
        echo "GitLab DAP Tracing Validation Script"
        echo ""
        echo "Usage: $0 {all|services|logs|processes|tools|help}"
        echo ""
        echo "Commands:"
        echo "  all       - Run all validation tests (default)"
        echo "  services  - Test service availability"
        echo "  logs      - Test log file availability and content"
        echo "  processes - Test filtering processes"
        echo "  tools     - Test analysis tools"
        echo "  help      - Show this help message"
        ;;
esac
