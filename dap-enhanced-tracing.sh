#!/bin/bash

# Enhanced GitLab DAP Tracing Script
# Provides comprehensive, structured tracing across all DAP components
# with correlation IDs, reduced noise, and agent orchestration visibility

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
TRACE_DIR="dap-traces"
GDK_PATH="../gdk"
CORRELATION_ID_PATTERN="correlation_id|x-request-id|x-gitlab-correlation-id"
AGENT_PATTERN="agent|tool|workflow|orchestration|planning|execution"
DAP_PATTERN="duo|dap|agent_platform|workflow"

print_header() {
    echo -e "${PURPLE}🔍 GitLab DAP Enhanced Tracing${NC}"
    echo -e "${PURPLE}=================================${NC}"
}

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_trace() {
    echo -e "${CYAN}🔗 $1${NC}"
}

# Setup enhanced tracing environment
setup_tracing_env() {
    print_info "Setting up enhanced tracing environment..."
    
    # Create trace directory structure
    mkdir -p "$TRACE_DIR"/{raw,filtered,correlation,agents,tools}
    
    # Create correlation tracking file
    touch "$TRACE_DIR/correlation/active_requests.log"
    
    print_status "Tracing environment ready at $TRACE_DIR/"
}

# Configure VS Code Extension for enhanced logging
configure_vscode_logging() {
    print_info "Configuring VS Code extension for enhanced logging..."
    
    # Create VS Code settings for maximum verbosity
    cat > "$TRACE_DIR/vscode-settings.json" << 'EOF'
{
    "gitlab.debug": true,
    "gitlab.duoAgentPlatform.enabled": true,
    "gitlab.duoAgentPlatform.connectionType": "grpc",
    "gitlab.duoChat.enabled": true
}
EOF
    
    # Set environment variables for extension debugging
    export GITLAB_DEBUG=true
    export VSCODE_GITLAB_VERBOSE_LOGGING=true
    export GITLAB_DAP_TRACE_ENABLED=true
    
    print_status "VS Code extension configured for enhanced logging"
    print_info "Apply settings from: $TRACE_DIR/vscode-settings.json"
}

# Configure AI Gateway for structured logging
configure_ai_gateway_logging() {
    print_info "Configuring AI Gateway for structured logging..."
    
    # Create enhanced AI Gateway configuration
    cat > "$TRACE_DIR/ai-gateway-enhanced.env" << 'EOF'
# Enhanced AI Gateway Logging Configuration
AIGW_LOGGING__LEVEL=debug
AIGW_LOGGING__FORMAT_JSON=true
AIGW_LOGGING__TO_FILE=./ai-gateway-enhanced.log
AIGW_LOGGING__ENABLE_REQUEST_LOGGING=true
AIGW_LOGGING__ENABLE_LITELLM_LOGGING=true

# Distributed tracing
LANGCHAIN_TRACING_V2=true
LANGSMITH_PROJECT=dap-enhanced-tracing
LANGSMITH_ENDPOINT=https://api.smith.langchain.com

# Correlation tracking
AIGW_CORRELATION_TRACKING=true
AIGW_AGENT_ORCHESTRATION_LOGGING=true
EOF
    
    print_status "AI Gateway enhanced logging configuration created"
    print_info "Apply configuration from: $TRACE_DIR/ai-gateway-enhanced.env"
}

# Configure Duo Workflow Service for detailed tracing
configure_duo_workflow_logging() {
    print_info "Configuring Duo Workflow Service for detailed tracing..."
    
    # Create enhanced Duo Workflow configuration
    cat > "$TRACE_DIR/duo-workflow-enhanced.env" << 'EOF'
# Enhanced Duo Workflow Service Logging Configuration
DUO_WORKFLOW_LOGGING__LEVEL=DEBUG
DUO_WORKFLOW_LOGGING__JSON_FORMAT=true
DUO_WORKFLOW_LOGGING__TO_FILE=./duo-workflow-enhanced.log

# Agent orchestration tracing
DEBUG=true
AGENT_ORCHESTRATION_TRACE=true
TOOL_EXECUTION_TRACE=true
CONTEXT_GATHERING_TRACE=true

# gRPC tracing
GRPC_TRACE=http_keepalive,call_error,connectivity_state,channel,subchannel
GRPC_VERBOSITY=DEBUG

# LangChain tracing
LANGCHAIN_TRACING_V2=true
LANGSMITH_PROJECT=dap-enhanced-tracing
LANGCHAIN_CALLBACKS_MANAGER=true

# Sentry error tracking
SENTRY_ERROR_TRACKING_ENABLED=true
SENTRY_TRACES_SAMPLE_RATE=1.0

# Prometheus metrics
PROMETHEUS_MULTIPROC_DIR=/tmp/duo-workflow-metrics
EOF
    
    print_status "Duo Workflow Service enhanced logging configuration created"
    print_info "Apply configuration from: $TRACE_DIR/duo-workflow-enhanced.env"
}

# Start intelligent log filtering and correlation
start_intelligent_filtering() {
    print_info "Starting intelligent log filtering and correlation..."
    
    # VS Code Extension filtered logging
    if command -v code >/dev/null 2>&1; then
        print_trace "Setting up VS Code extension trace filtering..."
        # This will be captured from VS Code Output panel
        touch "$TRACE_DIR/filtered/vscode-dap.log"
    fi
    
    # AI Gateway intelligent filtering
    if [ -f "$GDK_PATH/log/gitlab-ai-gateway/gateway_debug.log" ]; then
        print_trace "Starting AI Gateway intelligent filtering..."
        tail -f "$GDK_PATH/log/gitlab-ai-gateway/gateway_debug.log" | \
        grep -E "($CORRELATION_ID_PATTERN|$DAP_PATTERN|$AGENT_PATTERN)" | \
        jq -r 'select(.message | test("duo|agent|workflow|tool")) | 
               "\(.timestamp) [\(.correlation_id // "no-correlation")] \(.level) \(.logger_name): \(.message)"' \
        > "$TRACE_DIR/filtered/ai-gateway-dap.log" 2>/dev/null &
        echo $! > "$TRACE_DIR/pids/ai-gateway-filter.pid"
    fi
    
    # Duo Workflow Service intelligent filtering
    if [ -f "$GDK_PATH/log/duo-workflow-service/current" ]; then
        print_trace "Starting Duo Workflow Service intelligent filtering..."
        tail -f "$GDK_PATH/log/duo-workflow-service/current" | \
        grep -E "($CORRELATION_ID_PATTERN|$AGENT_PATTERN|tool_|workflow_)" | \
        jq -r 'select(.event | test("agent|tool|workflow|orchestration")) | 
               "\(.timestamp) [\(.correlation_id // "no-correlation")] \(.level) \(.logger): \(.event)"' \
        > "$TRACE_DIR/filtered/duo-workflow-agents.log" 2>/dev/null &
        echo $! > "$TRACE_DIR/pids/duo-workflow-filter.pid"
    fi
    
    # GitLab Rails DAP-specific filtering
    if [ -f "$GDK_PATH/gitlab/log/development.log" ]; then
        print_trace "Starting GitLab Rails DAP filtering..."
        tail -f "$GDK_PATH/gitlab/log/development.log" | \
        grep -E "(duo_agent_platform|ai/duo|DAP|agent_platform)" | \
        grep -v "asset\|static\|css\|js\|png\|jpg" \
        > "$TRACE_DIR/filtered/gitlab-dap.log" &
        echo $! > "$TRACE_DIR/pids/gitlab-filter.pid"
    fi
    
    print_status "Intelligent filtering started for all components"
}

# Start correlation tracking
start_correlation_tracking() {
    print_info "Starting correlation ID tracking..."
    
    mkdir -p "$TRACE_DIR/pids"
    
    # Correlation tracker script
    cat > "$TRACE_DIR/correlation-tracker.sh" << 'EOF'
#!/bin/bash
TRACE_DIR="dap-traces"
while true; do
    # Extract correlation IDs from all filtered logs
    grep -h -o -E "(correlation_id|x-request-id|x-gitlab-correlation-id)[\":]?\s*[\"']?([a-f0-9-]{36}|[a-zA-Z0-9-]{8,})" \
        "$TRACE_DIR/filtered/"*.log 2>/dev/null | \
    sed -E 's/.*[\":]?\s*[\"']?([a-f0-9-]{36}|[a-zA-Z0-9-]{8,})[\"']?.*/\1/' | \
    sort -u > "$TRACE_DIR/correlation/current_ids.tmp"
    
    if [ -f "$TRACE_DIR/correlation/current_ids.tmp" ]; then
        mv "$TRACE_DIR/correlation/current_ids.tmp" "$TRACE_DIR/correlation/current_ids.log"
    fi
    
    sleep 5
done
EOF
    
    chmod +x "$TRACE_DIR/correlation-tracker.sh"
    "$TRACE_DIR/correlation-tracker.sh" &
    echo $! > "$TRACE_DIR/pids/correlation-tracker.pid"
    
    print_status "Correlation tracking started"
}

# Start agent orchestration monitoring
start_agent_monitoring() {
    print_info "Starting agent orchestration monitoring..."
    
    # Agent activity tracker
    cat > "$TRACE_DIR/agent-monitor.sh" << 'EOF'
#!/bin/bash
TRACE_DIR="dap-traces"
while true; do
    # Track agent transitions and tool executions
    if [ -f "$TRACE_DIR/filtered/duo-workflow-agents.log" ]; then
        tail -n 50 "$TRACE_DIR/filtered/duo-workflow-agents.log" | \
        grep -E "(ChatAgent|PlannerAgent|ToolsExecutor|HandoverAgent)" | \
        tail -n 10 > "$TRACE_DIR/agents/current_activity.log"
        
        # Track tool executions
        tail -n 50 "$TRACE_DIR/filtered/duo-workflow-agents.log" | \
        grep -E "(tool_call|tool_response|tool_execution)" | \
        tail -n 10 > "$TRACE_DIR/tools/current_executions.log"
    fi
    
    sleep 2
done
EOF
    
    chmod +x "$TRACE_DIR/agent-monitor.sh"
    "$TRACE_DIR/agent-monitor.sh" &
    echo $! > "$TRACE_DIR/pids/agent-monitor.pid"
    
    print_status "Agent orchestration monitoring started"
}

# Create real-time dashboard
create_dashboard() {
    print_info "Creating real-time DAP tracing dashboard..."

    cat > "$TRACE_DIR/dashboard.sh" << 'EOF'
#!/bin/bash
TRACE_DIR="dap-traces"

clear
echo "🔍 GitLab DAP Real-Time Tracing Dashboard"
echo "========================================"
echo ""

while true; do
    # Move cursor to top
    tput cup 4 0

    echo "📊 Active Correlation IDs:"
    if [ -f "$TRACE_DIR/correlation/current_ids.log" ]; then
        head -n 5 "$TRACE_DIR/correlation/current_ids.log" | sed 's/^/  🔗 /'
    else
        echo "  No active requests"
    fi
    echo ""

    echo "🤖 Current Agent Activity:"
    if [ -f "$TRACE_DIR/agents/current_activity.log" ]; then
        tail -n 3 "$TRACE_DIR/agents/current_activity.log" | sed 's/^/  ⚡ /'
    else
        echo "  No agent activity"
    fi
    echo ""

    echo "🔧 Recent Tool Executions:"
    if [ -f "$TRACE_DIR/tools/current_executions.log" ]; then
        tail -n 3 "$TRACE_DIR/tools/current_executions.log" | sed 's/^/  🛠️  /'
    else
        echo "  No tool executions"
    fi
    echo ""

    echo "📝 Latest DAP Events:"
    find "$TRACE_DIR/filtered" -name "*.log" -exec tail -n 1 {} \; 2>/dev/null | \
    head -n 3 | sed 's/^/  📋 /'

    echo ""
    echo "Press Ctrl+C to exit dashboard"

    sleep 3
done
EOF

    chmod +x "$TRACE_DIR/dashboard.sh"
    print_status "Dashboard created: $TRACE_DIR/dashboard.sh"
}

# Create trace analysis tools
create_analysis_tools() {
    print_info "Creating trace analysis tools..."

    # Create correlation ID tracker
    cat > "$TRACE_DIR/trace-correlation.sh" << 'EOF'
#!/bin/bash
# Trace a specific correlation ID across all services
CORRELATION_ID="$1"
TRACE_DIR="dap-traces"

if [ -z "$CORRELATION_ID" ]; then
    echo "Usage: $0 <correlation-id>"
    echo "Available correlation IDs:"
    if [ -f "$TRACE_DIR/correlation/current_ids.log" ]; then
        head -n 10 "$TRACE_DIR/correlation/current_ids.log"
    fi
    exit 1
fi

echo "🔍 Tracing correlation ID: $CORRELATION_ID"
echo "============================================"

# Search across all filtered logs
find "$TRACE_DIR/filtered" -name "*.log" -exec grep -l "$CORRELATION_ID" {} \; | while read logfile; do
    service=$(basename "$logfile" .log)
    echo ""
    echo "📋 Service: $service"
    echo "-------------------"
    grep "$CORRELATION_ID" "$logfile" | head -n 10
done
EOF

    chmod +x "$TRACE_DIR/trace-correlation.sh"

    # Create agent flow analyzer
    cat > "$TRACE_DIR/analyze-agent-flow.sh" << 'EOF'
#!/bin/bash
# Analyze agent orchestration flow
TRACE_DIR="dap-traces"
WORKFLOW_ID="$1"

echo "🤖 Agent Orchestration Flow Analysis"
echo "===================================="

if [ -n "$WORKFLOW_ID" ]; then
    echo "Analyzing workflow: $WORKFLOW_ID"
    grep -r "$WORKFLOW_ID" "$TRACE_DIR/filtered/" | grep -i "agent\|orchestration\|handover"
else
    echo "Recent agent activities:"
    find "$TRACE_DIR/filtered" -name "*.log" -exec grep -l "agent\|orchestration" {} \; | \
    xargs grep -h "agent\|orchestration" | tail -n 20
fi
EOF

    chmod +x "$TRACE_DIR/analyze-agent-flow.sh"

    print_status "Analysis tools created in $TRACE_DIR/"
}

# Main execution
case "${1:-help}" in
    "start")
        print_header
        setup_tracing_env
        configure_vscode_logging
        configure_ai_gateway_logging
        configure_duo_workflow_logging
        start_intelligent_filtering
        start_correlation_tracking
        start_agent_monitoring
        create_dashboard
        create_analysis_tools
        
        print_status "Enhanced DAP tracing is now active!"
        print_info "📁 Trace files: $TRACE_DIR/"
        print_info "📊 Dashboard: $TRACE_DIR/dashboard.sh"
        print_info "🔧 Configuration files created for all services"
        print_warning "Apply the .env configurations to your services and restart them"
        ;;
    "stop")
        print_info "Stopping enhanced DAP tracing..."
        if [ -d "$TRACE_DIR/pids" ]; then
            for pidfile in "$TRACE_DIR/pids"/*.pid; do
                if [ -f "$pidfile" ]; then
                    kill $(cat "$pidfile") 2>/dev/null || true
                    rm "$pidfile"
                fi
            done
        fi
        print_status "Enhanced DAP tracing stopped"
        ;;
    "dashboard")
        if [ -f "$TRACE_DIR/dashboard.sh" ]; then
            "$TRACE_DIR/dashboard.sh"
        else
            print_error "Dashboard not found. Run 'start' first."
        fi
        ;;
    "status")
        print_info "Enhanced DAP Tracing Status:"
        if [ -d "$TRACE_DIR" ]; then
            echo "📁 Trace directory: ✅"
            echo "📊 Active processes:"
            if [ -d "$TRACE_DIR/pids" ]; then
                for pidfile in "$TRACE_DIR/pids"/*.pid; do
                    if [ -f "$pidfile" ]; then
                        pid=$(cat "$pidfile")
                        if kill -0 "$pid" 2>/dev/null; then
                            echo "  ✅ $(basename "$pidfile" .pid): $pid"
                        else
                            echo "  ❌ $(basename "$pidfile" .pid): stopped"
                        fi
                    fi
                done
            fi
        else
            echo "❌ Tracing not started"
        fi
        ;;
    "help"|*)
        echo "Enhanced GitLab DAP Tracing Script"
        echo ""
        echo "Usage: $0 {start|stop|dashboard|status|help}"
        echo ""
        echo "Commands:"
        echo "  start     - Start enhanced DAP tracing with intelligent filtering"
        echo "  stop      - Stop all tracing processes"
        echo "  dashboard - Show real-time tracing dashboard"
        echo "  status    - Show tracing status"
        echo "  help      - Show this help message"
        echo ""
        echo "Features:"
        echo "  🔗 Correlation ID tracking across all services"
        echo "  🤖 Agent orchestration monitoring"
        echo "  🔧 Tool execution tracing"
        echo "  📊 Real-time dashboard"
        echo "  🎯 Intelligent noise filtering"
        ;;
esac
