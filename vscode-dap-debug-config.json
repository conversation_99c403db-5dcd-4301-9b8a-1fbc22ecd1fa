{"version": "0.2.0", "configurations": [{"name": "Debug Extension with Enhanced DAP Tracing", "type": "extensionHost", "request": "launch", "args": ["--extensionDevelopmentPath=${workspaceFolder}", "--disable-extensions"], "outFiles": ["${workspaceFolder}/dist-desktop/**/*.js"], "preLaunchTask": "npm: build-desktop", "env": {"GITLAB_DEBUG": "true", "VSCODE_GITLAB_VERBOSE_LOGGING": "true", "GITLAB_DAP_TRACE_ENABLED": "true", "GITLAB_DAP_CORRELATION_TRACKING": "true", "GITLAB_DAP_AGENT_ORCHESTRATION_LOGGING": "true", "GITLAB_DAP_TOOL_EXECUTION_LOGGING": "true", "NODE_ENV": "development"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}, {"name": "Debug Extension with Maximum Verbosity", "type": "extensionHost", "request": "launch", "args": ["--extensionDevelopmentPath=${workspaceFolder}", "--disable-extensions", "--log-level=trace"], "outFiles": ["${workspaceFolder}/dist-desktop/**/*.js"], "preLaunchTask": "npm: build-desktop", "env": {"GITLAB_DEBUG": "true", "VSCODE_GITLAB_VERBOSE_LOGGING": "true", "GITLAB_DAP_TRACE_ENABLED": "true", "GITLAB_DAP_CORRELATION_TRACKING": "true", "GITLAB_DAP_AGENT_ORCHESTRATION_LOGGING": "true", "GITLAB_DAP_TOOL_EXECUTION_LOGGING": "true", "GITLAB_DAP_GRPC_TRACE": "true", "GITLAB_DAP_WEBSOCKET_TRACE": "true", "GITLAB_DAP_API_TRACE": "true", "NODE_ENV": "development", "DEBUG": "*gitlab*,*dap*,*duo*"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "trace": "verbose"}], "compounds": [{"name": "Full DAP Debugging Stack", "configurations": ["Debug Extension with Enhanced DAP Tracing"], "stopAll": true}]}