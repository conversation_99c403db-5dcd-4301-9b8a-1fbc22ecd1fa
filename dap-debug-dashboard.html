<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GitLab DAP Debug Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            background: #1a1a1a;
            color: #e0e0e0;
            line-height: 1.4;
        }
        
        .header {
            background: #2d2d2d;
            padding: 1rem;
            border-bottom: 2px solid #4a4a4a;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            color: #ff6b35;
            font-size: 1.5rem;
        }
        
        .status {
            display: flex;
            gap: 1rem;
        }
        
        .status-item {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
        }
        
        .status-active { background: #28a745; }
        .status-warning { background: #ffc107; color: #000; }
        .status-error { background: #dc3545; }
        
        .dashboard {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: auto auto auto;
            gap: 1rem;
            padding: 1rem;
            height: calc(100vh - 80px);
        }
        
        .panel {
            background: #2d2d2d;
            border: 1px solid #4a4a4a;
            border-radius: 8px;
            padding: 1rem;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        
        .panel-header {
            color: #ff6b35;
            font-weight: bold;
            margin-bottom: 0.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #4a4a4a;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .panel-content {
            flex: 1;
            overflow-y: auto;
            font-size: 0.85rem;
        }
        
        .correlation-list {
            grid-column: 1 / 2;
            grid-row: 1 / 2;
        }
        
        .agent-activity {
            grid-column: 2 / 3;
            grid-row: 1 / 2;
        }
        
        .tool-executions {
            grid-column: 1 / 2;
            grid-row: 2 / 3;
        }
        
        .request-flow {
            grid-column: 2 / 3;
            grid-row: 2 / 3;
        }
        
        .logs-panel {
            grid-column: 1 / 3;
            grid-row: 3 / 4;
        }
        
        .correlation-item {
            padding: 0.5rem;
            margin: 0.25rem 0;
            background: #3a3a3a;
            border-radius: 4px;
            border-left: 3px solid #007acc;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .correlation-item:hover {
            background: #4a4a4a;
        }
        
        .correlation-item.active {
            border-left-color: #ff6b35;
            background: #4a3a2a;
        }
        
        .correlation-id {
            font-family: monospace;
            color: #61dafb;
            font-size: 0.8rem;
        }
        
        .correlation-services {
            color: #98d982;
            font-size: 0.75rem;
            margin-top: 0.25rem;
        }
        
        .agent-step {
            padding: 0.5rem;
            margin: 0.25rem 0;
            background: #3a3a3a;
            border-radius: 4px;
            border-left: 3px solid #28a745;
        }
        
        .agent-name {
            color: #ffc107;
            font-weight: bold;
        }
        
        .agent-action {
            color: #98d982;
            font-size: 0.8rem;
            margin-top: 0.25rem;
        }
        
        .tool-execution {
            padding: 0.5rem;
            margin: 0.25rem 0;
            background: #3a3a3a;
            border-radius: 4px;
            border-left: 3px solid #17a2b8;
        }
        
        .tool-name {
            color: #17a2b8;
            font-weight: bold;
        }
        
        .tool-status {
            font-size: 0.75rem;
            margin-top: 0.25rem;
        }
        
        .tool-success { color: #28a745; }
        .tool-error { color: #dc3545; }
        .tool-running { color: #ffc107; }
        
        .flow-step {
            padding: 0.5rem;
            margin: 0.25rem 0;
            background: #3a3a3a;
            border-radius: 4px;
            position: relative;
        }
        
        .flow-step::before {
            content: '';
            position: absolute;
            left: -10px;
            top: 50%;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #007acc;
            transform: translateY(-50%);
        }
        
        .flow-service {
            color: #ff6b35;
            font-weight: bold;
            font-size: 0.8rem;
        }
        
        .flow-message {
            color: #e0e0e0;
            font-size: 0.75rem;
            margin-top: 0.25rem;
        }
        
        .log-entry {
            padding: 0.25rem 0;
            border-bottom: 1px solid #3a3a3a;
            font-size: 0.75rem;
        }
        
        .log-timestamp {
            color: #6c757d;
        }
        
        .log-level {
            padding: 0.1rem 0.3rem;
            border-radius: 3px;
            font-size: 0.7rem;
            margin: 0 0.5rem;
        }
        
        .log-debug { background: #6c757d; }
        .log-info { background: #17a2b8; }
        .log-warn { background: #ffc107; color: #000; }
        .log-error { background: #dc3545; }
        
        .log-service {
            color: #ff6b35;
            font-weight: bold;
        }
        
        .log-message {
            color: #e0e0e0;
        }
        
        .refresh-btn {
            background: #007acc;
            color: white;
            border: none;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
        }
        
        .refresh-btn:hover {
            background: #0056b3;
        }
        
        .auto-refresh {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.8rem;
        }
        
        .auto-refresh input {
            margin-right: 0.25rem;
        }
        
        .metrics {
            display: flex;
            gap: 1rem;
            font-size: 0.8rem;
        }
        
        .metric {
            color: #61dafb;
        }
        
        .metric-value {
            color: #98d982;
            font-weight: bold;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .updating {
            animation: pulse 1s infinite;
        }
        
        .empty-state {
            text-align: center;
            color: #6c757d;
            font-style: italic;
            padding: 2rem;
        }
        
        .filter-controls {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            flex-wrap: wrap;
        }
        
        .filter-btn {
            background: #3a3a3a;
            color: #e0e0e0;
            border: 1px solid #4a4a4a;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.75rem;
        }
        
        .filter-btn.active {
            background: #007acc;
            border-color: #007acc;
        }
        
        .search-box {
            background: #3a3a3a;
            color: #e0e0e0;
            border: 1px solid #4a4a4a;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            width: 200px;
        }
        
        .search-box:focus {
            outline: none;
            border-color: #007acc;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 GitLab DAP Debug Dashboard</h1>
        <div class="status">
            <div class="status-item status-active" id="connection-status">Connected</div>
            <div class="status-item status-warning" id="trace-status">Tracing Active</div>
            <div class="metrics">
                <span class="metric">Requests: <span class="metric-value" id="request-count">0</span></span>
                <span class="metric">Agents: <span class="metric-value" id="agent-count">0</span></span>
                <span class="metric">Tools: <span class="metric-value" id="tool-count">0</span></span>
            </div>
        </div>
    </div>
    
    <div class="dashboard">
        <div class="panel correlation-list">
            <div class="panel-header">
                🔗 Active Correlation IDs
                <div class="auto-refresh">
                    <input type="checkbox" id="auto-refresh" checked>
                    <label for="auto-refresh">Auto-refresh</label>
                    <button class="refresh-btn" onclick="refreshCorrelations()">Refresh</button>
                </div>
            </div>
            <div class="panel-content" id="correlations-content">
                <div class="empty-state">No active requests</div>
            </div>
        </div>
        
        <div class="panel agent-activity">
            <div class="panel-header">
                🤖 Agent Orchestration
                <button class="refresh-btn" onclick="refreshAgents()">Refresh</button>
            </div>
            <div class="panel-content" id="agents-content">
                <div class="empty-state">No agent activity</div>
            </div>
        </div>
        
        <div class="panel tool-executions">
            <div class="panel-header">
                🔧 Tool Executions
                <button class="refresh-btn" onclick="refreshTools()">Refresh</button>
            </div>
            <div class="panel-content" id="tools-content">
                <div class="empty-state">No tool executions</div>
            </div>
        </div>
        
        <div class="panel request-flow">
            <div class="panel-header">
                📊 Request Flow
                <input type="text" class="search-box" placeholder="Filter by correlation ID..." id="flow-filter">
            </div>
            <div class="panel-content" id="flow-content">
                <div class="empty-state">Select a correlation ID to view flow</div>
            </div>
        </div>
        
        <div class="panel logs-panel">
            <div class="panel-header">
                📝 Live Logs
                <div class="filter-controls">
                    <button class="filter-btn active" data-level="all">All</button>
                    <button class="filter-btn" data-level="error">Errors</button>
                    <button class="filter-btn" data-level="warn">Warnings</button>
                    <button class="filter-btn" data-level="info">Info</button>
                    <button class="filter-btn" data-level="debug">Debug</button>
                    <input type="text" class="search-box" placeholder="Search logs..." id="log-search">
                </div>
            </div>
            <div class="panel-content" id="logs-content">
                <div class="empty-state">No logs available</div>
            </div>
        </div>
    </div>
    
    <script>
        // Dashboard state
        let selectedCorrelationId = null;
        let autoRefreshEnabled = true;
        let refreshInterval = null;
        let logFilter = 'all';
        let logSearchTerm = '';
        
        // Mock data for demonstration
        const mockData = {
            correlations: [
                {
                    id: 'abc123-def456-ghi789',
                    services: ['vscode', 'gitlab', 'ai-gateway', 'duo-workflow'],
                    entryCount: 15,
                    lastActivity: new Date()
                },
                {
                    id: 'xyz789-uvw456-rst123',
                    services: ['vscode', 'gitlab', 'ai-gateway'],
                    entryCount: 8,
                    lastActivity: new Date(Date.now() - 30000)
                }
            ],
            agents: [
                {
                    name: 'ChatAgent',
                    action: 'Processing user query',
                    status: 'running',
                    timestamp: new Date()
                },
                {
                    name: 'PlannerAgent',
                    action: 'Creating execution plan',
                    status: 'completed',
                    timestamp: new Date(Date.now() - 15000)
                }
            ],
            tools: [
                {
                    name: 'file_system_tool',
                    status: 'success',
                    duration: 1.2,
                    timestamp: new Date()
                },
                {
                    name: 'git_tool',
                    status: 'running',
                    duration: null,
                    timestamp: new Date(Date.now() - 5000)
                }
            ],
            logs: [
                {
                    timestamp: new Date(),
                    level: 'info',
                    service: 'duo-workflow',
                    message: 'Agent ChatAgent started execution'
                },
                {
                    timestamp: new Date(Date.now() - 1000),
                    level: 'debug',
                    service: 'ai-gateway',
                    message: 'Received request from GitLab Rails'
                }
            ]
        };
        
        // Initialize dashboard
        function initDashboard() {
            setupEventListeners();
            startAutoRefresh();
            refreshAll();
        }
        
        function setupEventListeners() {
            document.getElementById('auto-refresh').addEventListener('change', (e) => {
                autoRefreshEnabled = e.target.checked;
                if (autoRefreshEnabled) {
                    startAutoRefresh();
                } else {
                    stopAutoRefresh();
                }
            });
            
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                    e.target.classList.add('active');
                    logFilter = e.target.dataset.level;
                    refreshLogs();
                });
            });
            
            document.getElementById('log-search').addEventListener('input', (e) => {
                logSearchTerm = e.target.value.toLowerCase();
                refreshLogs();
            });
            
            document.getElementById('flow-filter').addEventListener('input', (e) => {
                // Filter flow by correlation ID
                refreshFlow();
            });
        }
        
        function startAutoRefresh() {
            if (refreshInterval) clearInterval(refreshInterval);
            refreshInterval = setInterval(refreshAll, 3000);
        }
        
        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
                refreshInterval = null;
            }
        }
        
        function refreshAll() {
            refreshCorrelations();
            refreshAgents();
            refreshTools();
            refreshLogs();
            updateMetrics();
        }
        
        function refreshCorrelations() {
            const content = document.getElementById('correlations-content');
            content.classList.add('updating');
            
            // Simulate API call
            setTimeout(() => {
                if (mockData.correlations.length === 0) {
                    content.innerHTML = '<div class="empty-state">No active requests</div>';
                } else {
                    content.innerHTML = mockData.correlations.map(corr => `
                        <div class="correlation-item ${selectedCorrelationId === corr.id ? 'active' : ''}" 
                             onclick="selectCorrelation('${corr.id}')">
                            <div class="correlation-id">${corr.id}</div>
                            <div class="correlation-services">${corr.services.join(' → ')} (${corr.entryCount} entries)</div>
                        </div>
                    `).join('');
                }
                content.classList.remove('updating');
            }, 500);
        }
        
        function refreshAgents() {
            const content = document.getElementById('agents-content');
            content.classList.add('updating');
            
            setTimeout(() => {
                if (mockData.agents.length === 0) {
                    content.innerHTML = '<div class="empty-state">No agent activity</div>';
                } else {
                    content.innerHTML = mockData.agents.map(agent => `
                        <div class="agent-step">
                            <div class="agent-name">${agent.name}</div>
                            <div class="agent-action">${agent.action}</div>
                            <div class="tool-status tool-${agent.status}">${agent.status}</div>
                        </div>
                    `).join('');
                }
                content.classList.remove('updating');
            }, 300);
        }
        
        function refreshTools() {
            const content = document.getElementById('tools-content');
            content.classList.add('updating');
            
            setTimeout(() => {
                if (mockData.tools.length === 0) {
                    content.innerHTML = '<div class="empty-state">No tool executions</div>';
                } else {
                    content.innerHTML = mockData.tools.map(tool => `
                        <div class="tool-execution">
                            <div class="tool-name">${tool.name}</div>
                            <div class="tool-status tool-${tool.status}">
                                ${tool.status} ${tool.duration ? `(${tool.duration}s)` : ''}
                            </div>
                        </div>
                    `).join('');
                }
                content.classList.remove('updating');
            }, 400);
        }
        
        function refreshLogs() {
            const content = document.getElementById('logs-content');
            
            let filteredLogs = mockData.logs;
            
            if (logFilter !== 'all') {
                filteredLogs = filteredLogs.filter(log => log.level === logFilter);
            }
            
            if (logSearchTerm) {
                filteredLogs = filteredLogs.filter(log => 
                    log.message.toLowerCase().includes(logSearchTerm) ||
                    log.service.toLowerCase().includes(logSearchTerm)
                );
            }
            
            if (filteredLogs.length === 0) {
                content.innerHTML = '<div class="empty-state">No logs match current filters</div>';
            } else {
                content.innerHTML = filteredLogs.map(log => `
                    <div class="log-entry">
                        <span class="log-timestamp">${log.timestamp.toLocaleTimeString()}</span>
                        <span class="log-level log-${log.level}">${log.level.toUpperCase()}</span>
                        <span class="log-service">${log.service}</span>
                        <span class="log-message">${log.message}</span>
                    </div>
                `).join('');
            }
        }
        
        function refreshFlow() {
            const content = document.getElementById('flow-content');
            
            if (!selectedCorrelationId) {
                content.innerHTML = '<div class="empty-state">Select a correlation ID to view flow</div>';
                return;
            }
            
            // Mock flow data
            const flowSteps = [
                { service: 'VS Code Extension', message: 'User initiated DAP request', timestamp: new Date() },
                { service: 'GitLab Rails', message: 'API request received and authenticated', timestamp: new Date(Date.now() - 1000) },
                { service: 'AI Gateway', message: 'Request forwarded to Duo Workflow Service', timestamp: new Date(Date.now() - 2000) },
                { service: 'Duo Workflow', message: 'Agent orchestration started', timestamp: new Date(Date.now() - 3000) }
            ];
            
            content.innerHTML = flowSteps.map(step => `
                <div class="flow-step">
                    <div class="flow-service">${step.service}</div>
                    <div class="flow-message">${step.message}</div>
                </div>
            `).join('');
        }
        
        function selectCorrelation(correlationId) {
            selectedCorrelationId = correlationId;
            refreshCorrelations();
            refreshFlow();
        }
        
        function updateMetrics() {
            document.getElementById('request-count').textContent = mockData.correlations.length;
            document.getElementById('agent-count').textContent = mockData.agents.length;
            document.getElementById('tool-count').textContent = mockData.tools.length;
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initDashboard);
    </script>
</body>
</html>
