{
  "gitlab.instanceUrl": "http://127.0.0.1:3000",
  "gitlab.debug": true,
  "gitlab.duoAgentPlatform.enabled": true,
  "gitlab.duoAgentPlatform.connectionType": "grpc",
  "gitlab.duoAgentPlatform.defaultNamespace": "",
  "gitlab.duoChat.enabled": true,
  "gitlab.duo.enabledWithoutGitlabProject": true,
  "gitlab.featureFlags": {
    "duo_agent_platform_enhanced_logging": true,
    "duo_agent_platform_correlation_tracking": true,
    "duo_agent_platform_tool_execution_tracing": true,
    "duo_agent_platform_agent_orchestration_logging": true
  },
  "gitlab.customQueries": [],
  "gitlab.ignoreCertificateErrors": false,
  "gitlab.trackingUrl": null,
  "gitlab.pipelineGitRemoteName": "origin",
  "gitlab.branchProtection": false,
  "gitlab.distributedBinaries.skipBinaryIntegrityChecks": false,
  
  // Enhanced logging settings
  "gitlab.logging.level": "debug",
  "gitlab.logging.enableRequestLogging": true,
  "gitlab.logging.enableResponseLogging": true,
  "gitlab.logging.enableCorrelationTracking": true,
  "gitlab.logging.enableAgentOrchestrationLogging": true,
  "gitlab.logging.enableToolExecutionLogging": true,
  "gitlab.logging.enableGrpcTracing": true,
  "gitlab.logging.enableWebSocketTracing": true,
  
  // DAP-specific settings
  "gitlab.dap.tracing.enabled": true,
  "gitlab.dap.tracing.correlationIds": true,
  "gitlab.dap.tracing.agentOrchestration": true,
  "gitlab.dap.tracing.toolExecution": true,
  "gitlab.dap.tracing.contextGathering": true,
  "gitlab.dap.tracing.planningPhase": true,
  "gitlab.dap.tracing.responseGeneration": true,
  "gitlab.dap.tracing.errorHandling": true,
  
  // gRPC connection settings
  "gitlab.grpc.keepAliveTime": 20000,
  "gitlab.grpc.keepAliveTimeout": 5000,
  "gitlab.grpc.keepAlivePermitWithoutCalls": true,
  "gitlab.grpc.maxReceiveMessageLength": 4194304,
  "gitlab.grpc.maxSendMessageLength": 4194304,
  "gitlab.grpc.enableTrace": true,
  "gitlab.grpc.verbosity": "DEBUG",
  
  // WebSocket settings
  "gitlab.websocket.enableTrace": true,
  "gitlab.websocket.reconnectInterval": 5000,
  "gitlab.websocket.maxReconnectAttempts": 10,
  
  // API settings
  "gitlab.api.enableTrace": true,
  "gitlab.api.timeout": 30000,
  "gitlab.api.retryAttempts": 3,
  "gitlab.api.retryDelay": 1000,
  
  // Language Server settings for enhanced debugging
  "gitlab.languageServer.enabled": true,
  "gitlab.languageServer.logLevel": "debug",
  "gitlab.languageServer.trace.server": "verbose",
  "gitlab.languageServer.duo.enabled": true,
  "gitlab.languageServer.duo.agentPlatform.enabled": true,
  "gitlab.languageServer.duo.agentPlatform.connectionType": "grpc",
  
  // Output panel settings
  "gitlab.outputPanel.showOnError": true,
  "gitlab.outputPanel.showOnWarning": false,
  "gitlab.outputPanel.showOnInfo": false,
  "gitlab.outputPanel.showOnDebug": false,
  "gitlab.outputPanel.maxLines": 10000,
  "gitlab.outputPanel.enableTimestamps": true,
  "gitlab.outputPanel.enableCorrelationIds": true,
  
  // Telemetry and analytics
  "gitlab.telemetry.enabled": true,
  "gitlab.telemetry.enableDapTracing": true,
  "gitlab.telemetry.enablePerformanceMetrics": true,
  "gitlab.telemetry.enableErrorTracking": true,
  
  // Development and debugging
  "gitlab.development.enableExperimentalFeatures": true,
  "gitlab.development.enableBetaFeatures": true,
  "gitlab.development.enableDebugMode": true,
  "gitlab.development.enableVerboseLogging": true,
  "gitlab.development.enableTracing": true,
  
  // HTTP client settings
  "gitlab.httpClient.timeout": 30000,
  "gitlab.httpClient.enableTrace": true,
  "gitlab.httpClient.enableRequestLogging": true,
  "gitlab.httpClient.enableResponseLogging": true,
  "gitlab.httpClient.enableErrorLogging": true,
  "gitlab.httpClient.userAgent": "GitLab-VSCode-Extension-Debug",
  
  // Cache settings
  "gitlab.cache.enabled": true,
  "gitlab.cache.ttl": 300000,
  "gitlab.cache.maxSize": 100,
  "gitlab.cache.enableTrace": true,
  
  // Security settings for development
  "gitlab.security.enableCertificateValidation": false,
  "gitlab.security.enableHostValidation": false,
  "gitlab.security.allowSelfSignedCertificates": true,
  
  // Performance monitoring
  "gitlab.performance.enableMetrics": true,
  "gitlab.performance.enableProfiling": true,
  "gitlab.performance.enableMemoryTracking": true,
  "gitlab.performance.enableCpuTracking": true,
  
  // Error handling
  "gitlab.errorHandling.enableDetailedErrors": true,
  "gitlab.errorHandling.enableStackTraces": true,
  "gitlab.errorHandling.enableErrorReporting": true,
  "gitlab.errorHandling.enableRetryMechanism": true,
  
  // VS Code specific settings for better debugging
  "editor.minimap.enabled": false,
  "editor.wordWrap": "on",
  "editor.fontSize": 12,
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,
  "files.autoSave": "afterDelay",
  "files.autoSaveDelay": 1000,
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/dist-desktop": true,
    "**/.git": true,
    "**/logs": false,
    "**/dap-traces": false
  },
  "files.watcherExclude": {
    "**/node_modules/**": true,
    "**/dist/**": true,
    "**/dist-desktop/**": true
  },
  
  // Output and terminal settings
  "output.smartScroll.enabled": true,
  "terminal.integrated.scrollback": 10000,
  "terminal.integrated.enableBell": false,
  
  // Debug console settings
  "debug.console.fontSize": 12,
  "debug.console.lineHeight": 18,
  "debug.console.wordWrap": true,
  "debug.openDebug": "openOnDebugBreak",
  "debug.showBreakpointsInOverviewRuler": true,
  "debug.showInlineBreakpointCandidates": true,
  
  // JSON formatting for logs
  "json.format.enable": true,
  "json.format.keepLines": false,
  "json.maxItemsComputed": 5000,
  
  // Extension development settings
  "extensions.autoUpdate": false,
  "extensions.ignoreRecommendations": false,
  "extensions.showRecommendationsOnlyOnDemand": false,
  
  // Workspace trust
  "security.workspace.trust.untrustedFiles": "open",
  "security.workspace.trust.enabled": false
}
