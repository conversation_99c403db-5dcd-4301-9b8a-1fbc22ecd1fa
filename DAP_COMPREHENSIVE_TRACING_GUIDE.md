# GitLab DAP Comprehensive Tracing Guide

This guide provides complete end-to-end tracing for GitLab Duo Agent Platform (DAP) with structured logging, correlation tracking, and agent orchestration visibility.

## 🚀 Quick Start

### 1. Start Enhanced DAP Tracing

```bash
# In your GDK directory
./dap-enhanced-tracing.sh start
```

This will:
- Set up intelligent log filtering across all services
- Start correlation ID tracking
- Begin agent orchestration monitoring
- Create analysis tools and dashboard

### 2. Configure Services

Apply the generated configuration files to each service:

#### AI Gateway Configuration
```bash
# Copy enhanced configuration
cp dap-traces/ai-gateway-enhanced.env ../gdk/gitlab-ai-gateway/.env

# Restart AI Gateway
cd ../gdk
gdk restart gitlab-ai-gateway
```

#### Duo Workflow Service Configuration
```bash
# Copy enhanced configuration  
cp dap-traces/duo-workflow-enhanced.env ../gdk/gitlab-ai-gateway/********************/.env

# Restart Duo Workflow Service
cd ../gdk
gdk restart duo-workflow-service
```

#### VS Code Extension Configuration
```bash
# Copy debug configuration to VS Code extension project
cp vscode-dap-debug-config.json ../gitlab-vscode-extension/.vscode/launch.json
cp vscode-dap-settings.json ../gitlab-vscode-extension/.vscode/settings.json
```

### 3. Launch VS Code Extension with Enhanced Debugging

```bash
cd ../gitlab-vscode-extension
code .
# Press F5 and select "Debug Extension with Enhanced DAP Tracing"
```

### 4. Start Real-Time Dashboard

```bash
# Option 1: Terminal Dashboard
./dap-traces/dashboard.sh

# Option 2: Web Dashboard
python3 dap-trace-analyzer.py --server --port 8080
# Open http://localhost:8080 in your browser
```

## 📊 Tracing Components

### Enhanced Logging Features

1. **Correlation ID Tracking**
   - Tracks requests across all services
   - Maintains request context throughout the flow
   - Enables end-to-end tracing

2. **Agent Orchestration Monitoring**
   - Tracks agent transitions and decisions
   - Monitors handovers between agents
   - Captures planning and execution phases

3. **Tool Execution Tracing**
   - Logs tool selection and invocation
   - Tracks tool execution time and results
   - Captures tool errors and retries

4. **Intelligent Noise Filtering**
   - Filters out non-DAP related logs
   - Focuses on AI/agent/workflow activities
   - Reduces log volume by 80-90%

### Service-Specific Enhancements

#### VS Code Extension
- Enhanced gRPC tracing
- Request/response logging
- Connection state monitoring
- User interaction tracking

#### GitLab Rails
- DAP API endpoint tracing
- Authentication/authorization logging
- Request forwarding tracking

#### AI Gateway
- Structured JSON logging
- Request/response correlation
- Model gateway interactions
- Distributed tracing support

#### Duo Workflow Service
- Agent execution tracing
- Tool orchestration logging
- Workflow state transitions
- Performance metrics

## 🔍 Analysis Tools

### 1. Correlation Tracking

```bash
# Trace specific correlation ID
./dap-traces/trace-correlation.sh ********************

# List active correlation IDs
./dap-traces/trace-correlation.sh
```

### 2. Agent Flow Analysis

```bash
# Analyze agent orchestration
./dap-traces/analyze-agent-flow.sh [workflow-id]
```

### 3. Comprehensive Analysis

```bash
# Generate summary report
python3 dap-trace-analyzer.py

# Analyze specific correlation ID
python3 dap-trace-analyzer.py --correlation-id ********************

# JSON output for integration
python3 dap-trace-analyzer.py --output json
```

## 🧪 Testing DAP Tracing

### Test Scenarios

#### 1. Basic Chat Query
```bash
# In VS Code Extension Development Host:
# 1. Open Command Palette (Cmd+Shift+P)
# 2. Run "GitLab: Open Duo Chat"
# 3. Send: "What is this project about?"
# 4. Watch traces in dashboard
```

#### 2. Code Analysis Request
```bash
# In VS Code Extension Development Host:
# 1. Open a code file
# 2. Select some code
# 3. Right-click → "GitLab Duo: Explain Code"
# 4. Monitor agent orchestration
```

#### 3. Tool-Heavy Request
```bash
# Send complex query that triggers multiple tools:
# "What files are in this repository and what's the latest commit?"
# This should trigger:
# - File system tools
# - Git tools
# - Multiple agent handovers
```

### Expected Trace Flow

1. **VS Code Extension**
   ```
   [INFO] DAP connection established
   [DEBUG] Serializing request: {...}
   [DEBUG] gRPC call to GitLab: /api/v4/ai/duo_agent_platform/...
   ```

2. **GitLab Rails**
   ```
   [INFO] API request received: POST /api/v4/ai/duo_agent_platform/workflows
   [DEBUG] Authentication successful for user: ...
   [DEBUG] Forwarding to AI Gateway: ...
   ```

3. **AI Gateway**
   ```
   [INFO] Request received from GitLab Rails
   [DEBUG] Correlation ID: ********************
   [DEBUG] Forwarding to Duo Workflow Service
   ```

4. **Duo Workflow Service**
   ```
   [INFO] Workflow started: software_development
   [DEBUG] Agent ChatAgent starting execution
   [DEBUG] Tool selection: [file_system_tool, git_tool]
   [INFO] Agent handover: ChatAgent -> ToolsExecutor
   [DEBUG] Tool execution: file_system_tool (1.2s)
   [INFO] Agent handover: ToolsExecutor -> HandoverAgent
   [INFO] Workflow completed successfully
   ```

## 📈 Performance Monitoring

### Key Metrics to Track

1. **Request Latency**
   - End-to-end response time
   - Service-specific latencies
   - Tool execution times

2. **Agent Performance**
   - Agent execution times
   - Handover frequencies
   - Decision-making latency

3. **Tool Efficiency**
   - Tool success rates
   - Average execution times
   - Error frequencies

4. **System Health**
   - Memory usage
   - CPU utilization
   - Connection stability

### Dashboard Metrics

The web dashboard provides real-time metrics:
- Active correlation IDs
- Agent activity status
- Tool execution status
- Error rates and patterns
- Performance trends

## 🔧 Troubleshooting

### Common Issues

1. **No Traces Appearing**
   - Check service configurations are applied
   - Verify services are restarted
   - Ensure log files exist and are writable

2. **Missing Correlation IDs**
   - Check gRPC interceptors are enabled
   - Verify header propagation
   - Ensure structured logging is configured

3. **Dashboard Not Loading**
   - Check Flask dependencies: `pip install flask flask-cors`
   - Verify port 8080 is available
   - Check trace directory exists

4. **Incomplete Agent Traces**
   - Verify enhanced logging is enabled
   - Check agent decorators are applied
   - Ensure workflow IDs are propagated

### Debug Commands

```bash
# Check service status
./dap-enhanced-tracing.sh status

# View raw logs
tail -f dap-traces/filtered/*.log

# Check correlation tracking
cat dap-traces/correlation/current_ids.log

# Monitor agent activity
tail -f dap-traces/agents/current_activity.log
```

## 🛑 Stopping Tracing

```bash
# Stop all tracing processes
./dap-enhanced-tracing.sh stop

# Stop dashboard server
# Ctrl+C in terminal or close browser
```

## 📝 Log File Locations

### Filtered Logs (Noise Reduced)
- `dap-traces/filtered/vscode-dap.log` - VS Code extension DAP activities
- `dap-traces/filtered/ai-gateway-dap.log` - AI Gateway DAP requests
- `dap-traces/filtered/duo-workflow-agents.log` - Agent orchestration
- `dap-traces/filtered/gitlab-dap.log` - GitLab Rails DAP APIs

### Raw Logs (Full Detail)
- `../gdk/gitlab/log/development.log` - GitLab Rails
- `../gdk/log/gitlab-ai-gateway/gateway_debug.log` - AI Gateway
- `../gdk/log/duo-workflow-service/current` - Duo Workflow Service
- VS Code Output Panel → "GitLab Workflow" - Extension logs

### Analysis Files
- `dap-traces/correlation/current_ids.log` - Active correlation IDs
- `dap-traces/agents/current_activity.log` - Recent agent activities
- `dap-traces/tools/current_executions.log` - Tool execution history

## 🎯 Next Steps

1. **Customize Filtering**: Modify filter patterns in `dap-enhanced-tracing.sh`
2. **Add Metrics**: Extend dashboard with custom metrics
3. **Integration**: Connect to external monitoring systems
4. **Alerting**: Set up alerts for error patterns
5. **Performance**: Add performance profiling and optimization

This comprehensive tracing setup provides complete visibility into DAP operations, enabling effective debugging, performance optimization, and system understanding.
