#!/usr/bin/env python3
"""
Enhanced Duo Workflow Service Logging Configuration
Adds comprehensive agent orchestration and tool execution tracing
"""

import structlog
from typing import Dict, Any
from contextvars import ContextVar
import json
import time

# Enhanced context variables for detailed tracing
agent_execution_context: ContextVar[Dict[str, Any]] = ContextVar("agent_execution_context", default={})
tool_execution_context: ContextVar[Dict[str, Any]] = ContextVar("tool_execution_context", default={})
workflow_orchestration_context: ContextVar[Dict[str, Any]] = ContextVar("workflow_orchestration_context", default={})

class AgentOrchestrationLogger:
    """Enhanced logger for agent orchestration tracing"""
    
    def __init__(self):
        self.logger = structlog.get_logger("agent_orchestration")
    
    def log_agent_start(self, agent_name: str, workflow_id: str, correlation_id: str, input_data: Dict = None):
        """Log agent execution start"""
        context = {
            'agent_name': agent_name,
            'workflow_id': workflow_id,
            'correlation_id': correlation_id,
            'phase': 'start',
            'timestamp': time.time(),
            'input_summary': self._summarize_input(input_data) if input_data else None
        }
        agent_execution_context.set(context)
        
        self.logger.info(
            f"Agent {agent_name} starting execution",
            agent_name=agent_name,
            workflow_id=workflow_id,
            correlation_id=correlation_id,
            phase="agent_start",
            input_summary=context['input_summary']
        )
    
    def log_agent_decision(self, agent_name: str, decision: str, reasoning: str = None):
        """Log agent decision making"""
        context = agent_execution_context.get({})
        
        self.logger.info(
            f"Agent {agent_name} made decision: {decision}",
            agent_name=agent_name,
            workflow_id=context.get('workflow_id'),
            correlation_id=context.get('correlation_id'),
            phase="agent_decision",
            decision=decision,
            reasoning=reasoning
        )
    
    def log_agent_tool_selection(self, agent_name: str, selected_tools: list, available_tools: list = None):
        """Log tool selection by agent"""
        context = agent_execution_context.get({})
        
        self.logger.info(
            f"Agent {agent_name} selected tools: {', '.join(selected_tools)}",
            agent_name=agent_name,
            workflow_id=context.get('workflow_id'),
            correlation_id=context.get('correlation_id'),
            phase="tool_selection",
            selected_tools=selected_tools,
            available_tools=available_tools,
            tool_count=len(selected_tools)
        )
    
    def log_agent_handover(self, from_agent: str, to_agent: str, handover_data: Dict = None):
        """Log agent handover"""
        context = agent_execution_context.get({})
        
        self.logger.info(
            f"Agent handover: {from_agent} -> {to_agent}",
            from_agent=from_agent,
            to_agent=to_agent,
            workflow_id=context.get('workflow_id'),
            correlation_id=context.get('correlation_id'),
            phase="agent_handover",
            handover_summary=self._summarize_input(handover_data) if handover_data else None
        )
    
    def log_agent_completion(self, agent_name: str, output_data: Dict = None, execution_time: float = None):
        """Log agent execution completion"""
        context = agent_execution_context.get({})
        
        self.logger.info(
            f"Agent {agent_name} completed execution",
            agent_name=agent_name,
            workflow_id=context.get('workflow_id'),
            correlation_id=context.get('correlation_id'),
            phase="agent_completion",
            execution_time=execution_time,
            output_summary=self._summarize_input(output_data) if output_data else None
        )
    
    def _summarize_input(self, data: Dict, max_length: int = 200) -> str:
        """Summarize input data for logging"""
        if not data:
            return None
        
        try:
            summary = json.dumps(data, default=str)
            if len(summary) > max_length:
                return summary[:max_length] + "..."
            return summary
        except Exception:
            return str(data)[:max_length]

class ToolExecutionLogger:
    """Enhanced logger for tool execution tracing"""
    
    def __init__(self):
        self.logger = structlog.get_logger("tool_execution")
    
    def log_tool_start(self, tool_name: str, tool_args: Dict, workflow_id: str, correlation_id: str):
        """Log tool execution start"""
        context = {
            'tool_name': tool_name,
            'workflow_id': workflow_id,
            'correlation_id': correlation_id,
            'start_time': time.time(),
            'args_summary': self._summarize_args(tool_args)
        }
        tool_execution_context.set(context)
        
        self.logger.info(
            f"Tool {tool_name} execution started",
            tool_name=tool_name,
            workflow_id=workflow_id,
            correlation_id=correlation_id,
            phase="tool_start",
            args_summary=context['args_summary']
        )
    
    def log_tool_progress(self, tool_name: str, progress_info: str):
        """Log tool execution progress"""
        context = tool_execution_context.get({})
        
        self.logger.debug(
            f"Tool {tool_name} progress: {progress_info}",
            tool_name=tool_name,
            workflow_id=context.get('workflow_id'),
            correlation_id=context.get('correlation_id'),
            phase="tool_progress",
            progress=progress_info
        )
    
    def log_tool_completion(self, tool_name: str, result: Any, execution_time: float = None, success: bool = True):
        """Log tool execution completion"""
        context = tool_execution_context.get({})
        
        self.logger.info(
            f"Tool {tool_name} execution {'completed' if success else 'failed'}",
            tool_name=tool_name,
            workflow_id=context.get('workflow_id'),
            correlation_id=context.get('correlation_id'),
            phase="tool_completion",
            success=success,
            execution_time=execution_time,
            result_summary=self._summarize_result(result)
        )
    
    def log_tool_error(self, tool_name: str, error: Exception, error_context: Dict = None):
        """Log tool execution error"""
        context = tool_execution_context.get({})
        
        self.logger.error(
            f"Tool {tool_name} execution error: {str(error)}",
            tool_name=tool_name,
            workflow_id=context.get('workflow_id'),
            correlation_id=context.get('correlation_id'),
            phase="tool_error",
            error_type=type(error).__name__,
            error_message=str(error),
            error_context=error_context
        )
    
    def _summarize_args(self, args: Dict, max_length: int = 150) -> str:
        """Summarize tool arguments for logging"""
        if not args:
            return "no args"
        
        try:
            summary = json.dumps(args, default=str)
            if len(summary) > max_length:
                return summary[:max_length] + "..."
            return summary
        except Exception:
            return str(args)[:max_length]
    
    def _summarize_result(self, result: Any, max_length: int = 200) -> str:
        """Summarize tool result for logging"""
        if result is None:
            return "no result"
        
        try:
            if isinstance(result, dict):
                summary = json.dumps(result, default=str)
            else:
                summary = str(result)
            
            if len(summary) > max_length:
                return summary[:max_length] + "..."
            return summary
        except Exception:
            return f"<{type(result).__name__}>"

class WorkflowOrchestrationLogger:
    """Enhanced logger for workflow orchestration tracing"""
    
    def __init__(self):
        self.logger = structlog.get_logger("workflow_orchestration")
    
    def log_workflow_start(self, workflow_id: str, workflow_type: str, goal: str, correlation_id: str):
        """Log workflow start"""
        context = {
            'workflow_id': workflow_id,
            'workflow_type': workflow_type,
            'goal': goal,
            'correlation_id': correlation_id,
            'start_time': time.time()
        }
        workflow_orchestration_context.set(context)
        
        self.logger.info(
            f"Workflow {workflow_type} started",
            workflow_id=workflow_id,
            workflow_type=workflow_type,
            correlation_id=correlation_id,
            phase="workflow_start",
            goal=goal[:100] + "..." if len(goal) > 100 else goal
        )
    
    def log_workflow_state_transition(self, workflow_id: str, from_state: str, to_state: str, trigger: str = None):
        """Log workflow state transition"""
        context = workflow_orchestration_context.get({})
        
        self.logger.info(
            f"Workflow state transition: {from_state} -> {to_state}",
            workflow_id=workflow_id,
            correlation_id=context.get('correlation_id'),
            phase="state_transition",
            from_state=from_state,
            to_state=to_state,
            trigger=trigger
        )
    
    def log_workflow_checkpoint(self, workflow_id: str, checkpoint_data: Dict):
        """Log workflow checkpoint"""
        context = workflow_orchestration_context.get({})
        
        self.logger.debug(
            f"Workflow checkpoint saved",
            workflow_id=workflow_id,
            correlation_id=context.get('correlation_id'),
            phase="checkpoint",
            checkpoint_size=len(str(checkpoint_data)) if checkpoint_data else 0
        )
    
    def log_workflow_completion(self, workflow_id: str, final_result: Any, execution_time: float = None, success: bool = True):
        """Log workflow completion"""
        context = workflow_orchestration_context.get({})
        
        self.logger.info(
            f"Workflow {'completed successfully' if success else 'failed'}",
            workflow_id=workflow_id,
            correlation_id=context.get('correlation_id'),
            phase="workflow_completion",
            success=success,
            execution_time=execution_time,
            result_summary=self._summarize_result(final_result)
        )
    
    def _summarize_result(self, result: Any, max_length: int = 200) -> str:
        """Summarize workflow result for logging"""
        if result is None:
            return "no result"
        
        try:
            if isinstance(result, dict):
                summary = json.dumps(result, default=str)
            else:
                summary = str(result)
            
            if len(summary) > max_length:
                return summary[:max_length] + "..."
            return summary
        except Exception:
            return f"<{type(result).__name__}>"

# Global logger instances
agent_logger = AgentOrchestrationLogger()
tool_logger = ToolExecutionLogger()
workflow_logger = WorkflowOrchestrationLogger()

# Enhanced logging decorators
def log_agent_execution(agent_name: str):
    """Decorator to log agent execution"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Extract context from args/kwargs
            workflow_id = kwargs.get('workflow_id', 'unknown')
            correlation_id = kwargs.get('correlation_id', 'unknown')
            
            agent_logger.log_agent_start(agent_name, workflow_id, correlation_id, kwargs)
            
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                execution_time = time.time() - start_time
                agent_logger.log_agent_completion(agent_name, result, execution_time)
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                agent_logger.log_agent_completion(agent_name, None, execution_time)
                raise
        
        return wrapper
    return decorator

def log_tool_execution(tool_name: str):
    """Decorator to log tool execution"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Extract context from args/kwargs
            workflow_id = kwargs.get('workflow_id', 'unknown')
            correlation_id = kwargs.get('correlation_id', 'unknown')
            
            tool_logger.log_tool_start(tool_name, kwargs, workflow_id, correlation_id)
            
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                execution_time = time.time() - start_time
                tool_logger.log_tool_completion(tool_name, result, execution_time, True)
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                tool_logger.log_tool_error(tool_name, e)
                raise
        
        return wrapper
    return decorator

# Export enhanced loggers
__all__ = [
    'agent_logger',
    'tool_logger', 
    'workflow_logger',
    'log_agent_execution',
    'log_tool_execution',
    'AgentOrchestrationLogger',
    'ToolExecutionLogger',
    'WorkflowOrchestrationLogger'
]
